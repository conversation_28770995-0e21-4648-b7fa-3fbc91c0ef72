{"chat.agent.enabled": true, "chat.agent.maxRequests": 15, "github.copilot.chat.agent.runTasks": true, "chat.mcp.discovery.enabled": true, "github.copilot.chat.agent.autoFix": true, "chat.tools.autoApprove": false, "cSpell.words": ["Agentic", "atlasing", "Biostatistician", "Cordova", "customresourcedefinitions", "dashboarded", "Decisioning", "eksctl", "elicitations", "filecomplete", "fintech", "fluxcd", "gamedev", "gitops", "implementability", "inclusivity", "ingressgateway", "istioctl", "metroidvania", "NACLs", "nodegroup", "platformconfigs", "Playfocus", "playtesting", "pointerdown", "pointerup", "Polyre<PERSON>", "replayability", "roguelike", "roomodes", "Runbook", "runbooks", "Shardable", "<PERSON>lock", "speedrunner", "tekton", "tilemap", "tileset", "<PERSON><PERSON>", "VNET"]}