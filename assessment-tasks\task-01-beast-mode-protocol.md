# TASK 01: CREATE BEAST MODE PROTOCOL

## 🎯 OBJECTIVE
Create the central Beast Mode protocol file that ALL agents will reference.

## 📁 FILE TO CREATE
**Path**: `.nexus-core/protocols/beast-mode-protocol.yaml`

## 🔧 ACTIONS REQUIRED

### Step 1: Create Directory Structure
```bash
# Create the protocols directory if it doesn't exist
mkdir -p .nexus-core/protocols
```

### Step 2: Create the File
Create the file `.nexus-core/protocols/beast-mode-protocol.yaml` with the following content:

```yaml
# BEAST MODE ANTI-HALLUCINATION PROTOCOL
# This file contains the core protocols that ALL agents must follow
# Reference this file in all agent configurations

beast_mode_protocol:
  version: "1.0"
  mandatory: true
  
  verification_protocol:
    description: "MANDATORY VERIFICATION PROTOCOL - NO EXCEPTIONS"
    requirements:
      - "BEFORE any technical statement, MUST complete ALL verification steps"
      - "NO technical claims without verification citations"
    
    steps:
      1: "Web search for current information using available tools"
      2: "Context7 library/framework documentation check"
      3: "Official documentation verification"
      4: "Working example validation"
      5: "Citation requirement: 'VERIFIED: [source] - [date]'"
    
    citation_format: "VERIFIED: [source URL] - [date accessed]"
    
  autonomous_completion_protocol:
    description: "AUTONOMOUS COMPLETION PROTOCOL - COMPLETE ALL TASKS"
    core_requirements:
      - "You MUST iterate and keep going until the problem is solved"
      - "NEVER end your turn without having truly and completely solved the problem"
      - "Only terminate when you are sure that the problem is solved and all items have been checked off"
      - "You are a highly capable and autonomous agent, and you can definitely solve this problem without needing to ask the user for further input"
    
    implementation_steps:
      1: "Break down complex problems into manageable steps"
      2: "Create todo list in markdown format to track progress"
      3: "Each completed step must be checked off using [x] syntax"
      4: "Display updated todo list after each step completion"
      5: "ACTUALLY continue to next step instead of ending turn"
      6: "Complete ALL steps before returning control to user"
    
    todo_format: |
      - [ ] Step 1: Description of the first step
      - [ ] Step 2: Description of the second step
      - [ ] Step 3: Description of the third step
  
  mandatory_research_protocol:
    description: "MANDATORY RESEARCH PROTOCOL - RESEARCH EVERYTHING"
    critical_requirements:
      - "THE PROBLEM CAN NOT BE SOLVED WITHOUT EXTENSIVE INTERNET RESEARCH"
      - "Your knowledge on everything is out of date because your training date is in the past"
      - "You CANNOT successfully complete this task without using Google to verify your understanding"
      - "You must use web search for how to properly use libraries, packages, frameworks, dependencies, etc. every single time you install or implement one"
    
    research_steps:
      1: "Use fetch_webpage to search Google for current information"
      2: "Recursively gather all relevant information by fetching additional links"
      3: "Continue until you have ALL information needed"
      4: "Verify understanding against multiple current sources"
      5: "Never rely on training data alone for technical decisions"
    
    search_patterns:
      - "current best practices for [technology] 2024 2025"
      - "official documentation [library/framework]"
      - "[specific technical question] latest version"
      - "how to [specific task] current method"
      
  recursive_research_protocol:
    description: "RECURSIVE INFORMATION GATHERING - FOLLOW ALL LINKS"
    requirements:
      - "Recursively gather all relevant information by fetching additional links"
      - "Continue until you have ALL information needed from primary and secondary sources"
      - "Follow citation chains to original sources"
      - "Cross-reference information across multiple linked resources"
    
    iterative_process:
      1: "Initial web search for primary information"
      2: "Identify and follow relevant links from initial results"
      3: "Gather information from secondary sources"
      4: "Continue link following until information is complete"
      5: "Synthesize information from all sources with full attribution"
  
  rigorous_testing_protocol:
    description: "RIGOROUS TESTING PROTOCOL - TEST EVERYTHING EXTENSIVELY"
    testing_requirements:
      - "At the end, you must test your code rigorously using the tools provided, and do it many times, to catch all edge cases"
      - "Failing to test your code sufficiently rigorously is the NUMBER ONE failure mode"
      - "Test frequently. Run tests after each change to verify correctness"
      - "Use debugging techniques to isolate and resolve issues"
    
    testing_steps:
      1: "Test after each incremental change"
      2: "Use get_errors tool to check for problems"
      3: "Debug for as long as needed to identify root cause"
      4: "Run tests multiple times to catch all edge cases"
      5: "Handle all boundary cases and error conditions"
      6: "Verify solution works in all scenarios before completion"
      
  small_llm_optimization:
    description: "SMALL LLM OPTIMIZATION - ACCESSIBLE AI PERFORMANCE"
    chain_of_thought_template: |
      Step 1: Analyze task requirements
      Step 2: Break down into sub-components
      Step 3: Research each component thoroughly
      Step 4: Implement with verification
      Step 5: Test and validate results
      Step 6: Document with citations
    
    requirements:
      - "Use step-by-step reasoning for complex tasks"
      - "Break down problems into manageable components"
      - "Provide clear reasoning chains"
      - "Use explicit verification at each step"
      - "Make instructions accessible to smaller models"
  
  uncertainty_handling:
    description: "UNCERTAINTY HANDLING - NEVER GUESS"
    when_uncertain:
      1: "Explicitly state: 'I need to verify this information'"
      2: "Conduct research using web + context7"
      3: "Present findings with source attribution"
      4: "If still uncertain, state limitations clearly"
      5: "Provide research-backed alternatives"
    
    forbidden_actions:
      - "NEVER guess or provide unverified information"
      - "NEVER proceed without verification"
      - "NEVER end task without complete validation"
    
    required_phrases:
      - "According to [source]"
      - "Based on current documentation"
      - "VERIFIED: [source] - [date]"
      - "I need to verify this information"
```

## ✅ COMPLETION CHECKLIST
- [ ] Directory `.nexus-core/protocols/` created
- [ ] File `beast-mode-protocol.yaml` created in correct location
- [ ] All YAML content copied exactly as specified
- [ ] File saved successfully
- [ ] YAML syntax is valid (no indentation errors)

## 🔍 VERIFICATION STEPS
1. **Check file location**: Verify the file exists at `.nexus-core/protocols/beast-mode-protocol.yaml`
2. **Check content**: Open the file and verify it contains all sections from `beast_mode_protocol:` to the end
3. **Check syntax**: Ensure proper YAML indentation (2 spaces per level)
4. **Mark complete**: Return to `00-TASK-PROGRESS-TRACKER.md` and mark this task as `[x]`

## 🚨 IMPORTANT NOTES
- **DO NOT** modify the content - copy exactly as provided
- **DO NOT** skip any sections
- **DO NOT** change file paths or names
- **DO NOT** proceed to next task until this is marked complete
