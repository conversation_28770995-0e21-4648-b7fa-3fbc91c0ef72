template:
  id: game-brief-template-v2
  name: Game Brief
  version: 2.0
  output:
    format: markdown
    filename: "docs/{{game_name}}-game-brief.md"
    title: "{{game_title}} Game Brief"

workflow:
  mode: interactive

sections:
  - id: initial-setup
    instruction: |
      This template creates a comprehensive game brief that serves as the foundation for all subsequent game development work. The brief should capture the essential vision, scope, and requirements needed to create a detailed Game Design Document.
      
      This brief is typically created early in the ideation process, often after brainstorming sessions, to crystallize the game concept before moving into detailed design.

  - id: game-vision
    title: Game Vision
    instruction: Establish the core vision and identity of the game. Present each subsection and gather user feedback before proceeding.
    sections:
      - id: core-concept
        title: Core Concept
        instruction: 2-3 sentences that clearly capture what the game is and why it will be compelling to players
      - id: elevator-pitch
        title: Elevator Pitch
        instruction: Single sentence that captures the essence of the game in a memorable way
        template: |
          **"{{game_description_in_one_sentence}}"**
      - id: vision-statement
        title: Vision Statement
        instruction: Inspirational statement about what the game will achieve for players and why it matters

  - id: target-market
    title: Target Market
    instruction: Define the audience and market context. Apply `tasks#advanced-elicitation` after presenting this section.
    sections:
      - id: primary-audience
        title: Primary Audience
        template: |
          **Demographics:** {{age_range}}, {{platform_preference}}, {{gaming_experience}}
          **Psychographics:** {{interests}}, {{motivations}}, {{play_patterns}}
          **Gaming Preferences:** {{preferred_genres}}, {{session_length}}, {{difficulty_preference}}
      - id: secondary-audiences
        title: Secondary Audiences
        template: |
          **Audience 2:** {{description}}
          **Audience 3:** {{description}}
      - id: market-context
        title: Market Context
        template: |
          **Genre:** {{primary_genre}} / {{secondary_genre}}
          **Platform Strategy:** {{platform_focus}}
          **Competitive Positioning:** {{differentiation_statement}}

  - id: game-fundamentals
    title: Game Fundamentals
    instruction: Define the core gameplay elements. Each subsection should be specific enough to guide detailed design work.
    sections:
      - id: core-gameplay-pillars
        title: Core Gameplay Pillars
        instruction: 3-5 fundamental principles that guide all design decisions
        type: numbered-list
        template: |
          **{{pillar_name}}** - {{description_and_rationale}}
      - id: primary-mechanics
        title: Primary Mechanics
        instruction: List the 3-5 most important gameplay mechanics that define the player experience
        repeatable: true
        template: |
          **Core Mechanic: {{mechanic_name}}**
          
          - **Description:** {{how_it_works}}
          - **Player Value:** {{why_its_fun}}
          - **Implementation Scope:** {{complexity_estimate}}
      - id: player-experience-goals
        title: Player Experience Goals
        instruction: Define what emotions and experiences the game should create for players
        template: |
          **Primary Experience:** {{main_emotional_goal}}
          **Secondary Experiences:** {{supporting_emotional_goals}}
          **Engagement Pattern:** {{how_player_engagement_evolves}}

  - id: scope-constraints
    title: Scope and Constraints
    instruction: Define the boundaries and limitations that will shape development. Apply `tasks#advanced-elicitation` to clarify any constraints.
    sections:
      - id: project-scope
        title: Project Scope
        template: |
          **Game Length:** {{estimated_content_hours}}
          **Content Volume:** {{levels_areas_content_amount}}
          **Feature Complexity:** {{simple|moderate|complex}}
          **Scope Comparison:** "Similar to {{reference_game}} but with {{key_differences}}"
      - id: technical-constraints
        title: Technical Constraints
        template: |
          **Platform Requirements:**
          
          - Primary: {{platform_1}} - {{requirements}}
          - Secondary: {{platform_2}} - {{requirements}}
          
          **Technical Specifications:**
          
          - Engine: Phaser 3 + TypeScript
          - Performance Target: {{fps_target}} FPS on {{target_device}}
          - Memory Budget: <{{memory_limit}}MB
          - Load Time Goal: <{{load_time_seconds}}s
      - id: resource-constraints
        title: Resource Constraints
        template: |
          **Team Size:** {{team_composition}}
          **Timeline:** {{development_duration}}
          **Budget Considerations:** {{budget_constraints_or_targets}}
          **Asset Requirements:** {{art_audio_content_needs}}
      - id: business-constraints
        title: Business Constraints
        condition: has_business_goals
        template: |
          **Monetization Model:** {{free|premium|freemium|subscription}}
          **Revenue Goals:** {{revenue_targets_if_applicable}}
          **Platform Requirements:** {{store_certification_needs}}
          **Launch Timeline:** {{target_launch_window}}

  - id: reference-framework
    title: Reference Framework
    instruction: Provide context through references and competitive analysis
    sections:
      - id: inspiration-games
        title: Inspiration Games
        sections:
          - id: primary-references
            title: Primary References
            type: numbered-list
            repeatable: true
            template: |
              **{{reference_game}}** - {{what_we_learn_from_it}}
      - id: competitive-analysis
        title: Competitive Analysis
        template: |
          **Direct Competitors:**
          
          - {{competitor_1}}: {{strengths_and_weaknesses}}
          - {{competitor_2}}: {{strengths_and_weaknesses}}
          
          **Differentiation Strategy:**
          {{how_we_differ_and_why_thats_valuable}}
      - id: market-opportunity
        title: Market Opportunity
        template: |
          **Market Gap:** {{underserved_need_or_opportunity}}
          **Timing Factors:** {{why_now_is_the_right_time}}
          **Success Metrics:** {{how_well_measure_success}}

  - id: content-framework
    title: Content Framework
    instruction: Outline the content structure and progression without full design detail
    sections:
      - id: game-structure
        title: Game Structure
        template: |
          **Overall Flow:** {{linear|hub_world|open_world|procedural}}
          **Progression Model:** {{how_players_advance}}
          **Session Structure:** {{typical_play_session_flow}}
      - id: content-categories
        title: Content Categories
        template: |
          **Core Content:**
          
          - {{content_type_1}}: {{quantity_and_description}}
          - {{content_type_2}}: {{quantity_and_description}}
          
          **Optional Content:**
          
          - {{optional_content_type}}: {{quantity_and_description}}
          
          **Replay Elements:**
          
          - {{replayability_features}}
      - id: difficulty-accessibility
        title: Difficulty and Accessibility
        template: |
          **Difficulty Approach:** {{how_challenge_is_structured}}
          **Accessibility Features:** {{planned_accessibility_support}}
          **Skill Requirements:** {{what_skills_players_need}}

  - id: art-audio-direction
    title: Art and Audio Direction
    instruction: Establish the aesthetic vision that will guide asset creation
    sections:
      - id: visual-style
        title: Visual Style
        template: |
          **Art Direction:** {{style_description}}
          **Reference Materials:** {{visual_inspiration_sources}}
          **Technical Approach:** {{2d_style_pixel_vector_etc}}
          **Color Strategy:** {{color_palette_mood}}
      - id: audio-direction
        title: Audio Direction
        template: |
          **Music Style:** {{genre_and_mood}}
          **Sound Design:** {{audio_personality}}
          **Implementation Needs:** {{technical_audio_requirements}}
      - id: ui-ux-approach
        title: UI/UX Approach
        template: |
          **Interface Style:** {{ui_aesthetic}}
          **User Experience Goals:** {{ux_priorities}}
          **Platform Adaptations:** {{cross_platform_considerations}}

  - id: risk-assessment
    title: Risk Assessment
    instruction: Identify potential challenges and mitigation strategies
    sections:
      - id: technical-risks
        title: Technical Risks
        type: table
        template: |
          | Risk | Probability | Impact | Mitigation Strategy |
          | ---- | ----------- | ------ | ------------------- |
          | {{technical_risk}} | {{high|med|low}} | {{high|med|low}} | {{mitigation_approach}} |
      - id: design-risks
        title: Design Risks
        type: table
        template: |
          | Risk | Probability | Impact | Mitigation Strategy |
          | ---- | ----------- | ------ | ------------------- |
          | {{design_risk}} | {{high|med|low}} | {{high|med|low}} | {{mitigation_approach}} |
      - id: market-risks
        title: Market Risks
        type: table
        template: |
          | Risk | Probability | Impact | Mitigation Strategy |
          | ---- | ----------- | ------ | ------------------- |
          | {{market_risk}} | {{high|med|low}} | {{high|med|low}} | {{mitigation_approach}} |

  - id: success-criteria
    title: Success Criteria
    instruction: Define measurable goals for the project
    sections:
      - id: player-experience-metrics
        title: Player Experience Metrics
        template: |
          **Engagement Goals:**
          
          - Tutorial completion rate: >{{percentage}}%
          - Average session length: {{duration}} minutes
          - Player retention: D1 {{d1}}%, D7 {{d7}}%, D30 {{d30}}%
          
          **Quality Benchmarks:**
          
          - Player satisfaction: >{{rating}}/10
          - Completion rate: >{{percentage}}%
          - Technical performance: {{fps_target}} FPS consistent
      - id: development-metrics
        title: Development Metrics
        template: |
          **Technical Targets:**
          
          - Zero critical bugs at launch
          - Performance targets met on all platforms
          - Load times under {{seconds}}s
          
          **Process Goals:**
          
          - Development timeline adherence
          - Feature scope completion
          - Quality assurance standards
      - id: business-metrics
        title: Business Metrics
        condition: has_business_goals
        template: |
          **Commercial Goals:**
          
          - {{revenue_target}} in first {{time_period}}
          - {{user_acquisition_target}} players in first {{time_period}}
          - {{retention_target}} monthly active users

  - id: next-steps
    title: Next Steps
    instruction: Define immediate actions following the brief completion
    sections:
      - id: immediate-actions
        title: Immediate Actions
        type: numbered-list
        template: |
          **{{action_item}}** - {{details_and_timeline}}
      - id: development-roadmap
        title: Development Roadmap
        sections:
          - id: phase-1-preproduction
            title: "Phase 1: Pre-Production ({{duration}})"
            type: bullet-list
            template: |
              - Detailed Game Design Document creation
              - Technical architecture planning
              - Art style exploration and pipeline setup
          - id: phase-2-prototype
            title: "Phase 2: Prototype ({{duration}})"
            type: bullet-list
            template: |
              - Core mechanic implementation
              - Technical proof of concept
              - Initial playtesting and iteration
          - id: phase-3-production
            title: "Phase 3: Production ({{duration}})"
            type: bullet-list
            template: |
              - Full feature development
              - Content creation and integration
              - Comprehensive testing and optimization
      - id: documentation-pipeline
        title: Documentation Pipeline
        sections:
          - id: required-documents
            title: Required Documents
            type: numbered-list
            template: |
              Game Design Document (GDD) - {{target_completion}}
              Technical Architecture Document - {{target_completion}}
              Art Style Guide - {{target_completion}}
              Production Plan - {{target_completion}}
      - id: validation-plan
        title: Validation Plan
        template: |
          **Concept Testing:**
          
          - {{validation_method_1}} - {{timeline}}
          - {{validation_method_2}} - {{timeline}}
          
          **Prototype Testing:**
          
          - {{testing_approach}} - {{timeline}}
          - {{feedback_collection_method}} - {{timeline}}

  - id: appendices
    title: Appendices
    sections:
      - id: research-materials
        title: Research Materials
        instruction: Include any supporting research, competitive analysis, or market data that informed the brief
      - id: brainstorming-notes
        title: Brainstorming Session Notes
        instruction: Reference any brainstorming sessions that led to this brief
      - id: stakeholder-input
        title: Stakeholder Input
        instruction: Include key input from stakeholders that shaped the vision
      - id: change-log
        title: Change Log
        instruction: Track document versions and changes
        type: table
        template: |
          | Date | Version | Description | Author |
          | :--- | :------ | :---------- | :----- |