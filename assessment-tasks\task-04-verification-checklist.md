# TASK 04: CREATE VERIFICATION CHECKLIST

## 🎯 OBJECTIVE
Create a comprehensive verification checklist that all agents must complete for every task.

## 📁 FILE TO CREATE
**Path**: `.nexus-core/validation/verification-checklist.md`

## 🔧 ACTIONS REQUIRED

### Step 1: Verify Directory Exists
```bash
# The validation directory should exist from previous task
# If not, create it:
mkdir -p .nexus-core/validation
```

### Step 2: Create the File
Create the file `.nexus-core/validation/verification-checklist.md` with the following content:

```markdown
# VERIFICATION CHECKLIST
# This checklist references the central Beast Mode protocol
# All agents must complete this checklist for every task

## Protocol Reference
- Beast Mode Protocol: `.nexus-core/protocols/beast-mode-protocol.yaml`
- Validation System: `.nexus-core/validation/protocol-validator.yaml`

## Pre-Implementation Verification
- [ ] Beast Mode protocol loaded and reviewed
- [ ] Web search completed for current information
- [ ] Context7 documentation checked
- [ ] Official documentation verified
- [ ] Working examples validated
- [ ] All sources cited with dates using format: "VERIFIED: [source] - [date]"

## Implementation Verification
- [ ] Code syntax verified against current documentation
- [ ] API usage validated with official examples
- [ ] Best practices confirmed through multiple sources
- [ ] Performance implications considered
- [ ] Security considerations addressed
- [ ] Todo list created and updated throughout process

## Post-Implementation Verification
- [ ] Rigorous testing completed (all edge cases)
- [ ] All error conditions tested
- [ ] Performance validated
- [ ] Documentation updated
- [ ] All todo items marked complete [x]
- [ ] Protocol compliance validated

## Mandatory Testing Checklist
- [ ] Unit tests for all functions
- [ ] Integration tests for components
- [ ] End-to-end testing scenarios
- [ ] Edge case validation
- [ ] Error condition handling
- [ ] Performance benchmarking
- [ ] Security vulnerability testing
- [ ] Cross-platform compatibility
- [ ] Regression testing
- [ ] Load testing (if applicable)

## Quality Assurance Checklist
- [ ] Code follows current best practices
- [ ] Documentation is complete and accurate
- [ ] All dependencies are up to date
- [ ] Security vulnerabilities addressed
- [ ] Performance optimizations implemented
- [ ] Accessibility requirements met
- [ ] Error handling is comprehensive
- [ ] User experience is optimized

## Citation Requirements
All technical claims must include:
- **Source URL**: Full URL to original source
- **Date Accessed**: When the information was verified
- **Format**: "VERIFIED: [source URL] - [date accessed]"
- **Context**: Brief description of what was verified

## Failure Handling
If any checklist item fails:
1. **STOP** current implementation
2. **RESEARCH** to resolve the issue
3. **VERIFY** the solution with current sources
4. **TEST** the corrected implementation
5. **DOCUMENT** the resolution process
6. **CONTINUE** only after validation passes

## Completion Criteria
This checklist is complete when:
- [ ] All items are checked off [x]
- [ ] All verification citations are present
- [ ] All testing has passed
- [ ] All quality assurance checks pass
- [ ] Documentation is complete
- [ ] Protocol compliance is validated

## Emergency Escalation
If unable to complete any checklist item:
1. Document the specific blocker
2. Include attempted solutions
3. Escalate to supervisor immediately
4. Do not proceed without resolution
```

## ✅ COMPLETION CHECKLIST
- [ ] Directory `.nexus-core/validation/` exists
- [ ] File `verification-checklist.md` created in correct location
- [ ] All markdown content copied exactly as specified
- [ ] File saved successfully
- [ ] Markdown formatting is correct

## 🔍 VERIFICATION STEPS
1. **Check file location**: Verify the file exists at `.nexus-core/validation/verification-checklist.md`
2. **Check content**: Open the file and verify it contains all sections from the header to completion criteria
3. **Check formatting**: Ensure proper markdown formatting with checkboxes `- [ ]`
4. **Mark complete**: Return to `00-TASK-PROGRESS-TRACKER.md` and mark this task as `[x]`

## 🚨 IMPORTANT NOTES
- **DO NOT** modify the content - copy exactly as provided
- **DO NOT** skip any sections
- **DO NOT** change file paths or names
- **DO NOT** proceed to next task until this is marked complete
