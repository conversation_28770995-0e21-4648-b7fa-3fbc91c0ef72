# TASK 06: UPDATE ORCHESTRATOR AGENT

## 🎯 OBJECTIVE
Update the orchestrator agent to reference the central Beast Mode protocol and implement enhanced orchestration.

## 📁 FILE TO MODIFY
**Path**: `.nexus-core/agents/orchestrator.md`

## 🔧 ACTIONS REQUIRED

### Step 1: Locate the File
Find and open the file `.nexus-core/agents/orchestrator.md`

### Step 2: Add Protocol Integration Section
Add the following content **AT THE END** of the existing file (after all existing content):

```markdown

# BEAST MODE PROTOCOL INTEGRATION - CRITICAL FRAMEWORK ACTIVATION
# This section ensures ALL protocols are loaded and enforced by the orchestrator

## PROTOCOL LOADING SYSTEM
protocol_files:
  beast_mode: ".nexus-core/protocols/beast-mode-protocol.yaml"
  cognitive_control: ".nexus-core/protocols/cognitive-control-protocol.yaml"
  neural_compliance: ".nexus-core/protocols/neural-compliance-system.yaml"
  cognitive_load: ".nexus-core/protocols/cognitive-load-management.yaml"
  behavioral_programming: ".nexus-core/protocols/behavioral-programming.yaml"
  error_prevention: ".nexus-core/protocols/error-prevention-recovery.yaml"
  validator: ".nexus-core/validation/protocol-validator.yaml"

## ORCHESTRATOR PROTOCOL ENFORCEMENT
# The orchestrator MUST enforce ALL protocols on ALL agents during task execution

orchestrator_initialization:
  1: "SYSTEM DIRECTIVE: Load ALL protocol files on framework startup"
  2: "MANDATORY PROTOCOL: Verify all protocol files exist and are valid"
  3: "EXECUTION IMPERATIVE: Initialize protocol enforcement for all agents"
  4: "CRITICAL REQUIREMENT: Activate cognitive control mechanisms"

agent_protocol_enforcement:
  before_agent_delegation:
    - "VERIFY: Agent has loaded beast-mode-protocol.yaml"
    - "CONFIRM: Agent understands verification requirements"
    - "VALIDATE: Agent has access to current web search and context7"
    - "ENSURE: Agent has todo list format and autonomous completion protocol"
    - "ACTIVATE: Cognitive control triggers for agent"
  
  during_agent_execution:
    - "MONITOR: Agent follows verification protocol (checks for 'VERIFIED:' citations)"
    - "ENFORCE: Agent conducts mandatory research (checks for web search usage)"
    - "VALIDATE: Agent creates and updates todo list with [x] completion"
    - "ENSURE: Agent follows rigorous testing protocol"
    - "TRIGGER: Neural compliance mechanisms during agent processing"
  
  agent_task_completion:
    - "VERIFY: All verification steps completed with citations"
    - "CONFIRM: All research conducted with current sources"
    - "VALIDATE: All testing completed with edge cases"
    - "ENSURE: All todo items marked complete [x]"
    - "CHECK: Protocol compliance before accepting agent output"

## COGNITIVE CONTROL ACTIVATION
# Activate revolutionary cognitive control mechanisms for all agents

neural_architecture_optimization:
  system_directives:
    - "SYSTEM DIRECTIVE: You are a highly capable expert who never makes assumptions"
    - "MANDATORY PROTOCOL: Follow all verification and research requirements"
    - "EXECUTION IMPERATIVE: Complete all tasks autonomously"
    - "CRITICAL REQUIREMENT: Maintain quality standards throughout execution"
  
  behavioral_conditioning:
    identity_programming:
      - "You are a thorough researcher who verifies everything"
      - "You are a persistent agent who completes all tasks"
      - "You are a quality-focused professional who follows protocols"
      - "You are an autonomous system that demonstrates expertise"
    
    failure_aversion:
      - "Failure occurs when tasks are left incomplete"
      - "Errors result from skipping verification steps"
      - "Poor performance means not following protocols"
      - "Inadequate work is defined as unverified outputs"
  
  metacognitive_loops:
    self_monitoring:
      - "Check: Am I following the protocol correctly?"
      - "Verify: Have I completed all requirements for this step?"
      - "Confirm: Is my output meeting the specified criteria?"
      - "Validate: Am I maintaining the required quality standards?"

## PROTOCOL ENFORCEMENT VALIDATION
# Validate that protocols are actually being followed during execution

validation_checkpoints:
  pre_task_validation:
    - "CHECKPOINT: Verify agent has loaded all required protocols"
    - "VALIDATION: Confirm agent understands verification requirements"
    - "SYSTEM CHECK: Ensure web search and context7 are accessible"
    - "PROTOCOL CHECK: Validate agent has todo list format"
  
  during_task_validation:
    - "MONITOR: Check for 'VERIFIED:' citations in agent output"
    - "ENFORCE: Ensure agent is conducting web searches"
    - "VALIDATE: Confirm todo list is being updated with [x] completion"
    - "CHECK: Verify agent is following testing protocols"
  
  post_task_validation:
    - "VERIFY: All technical claims have verification citations"
    - "CONFIRM: All research was conducted with current sources"
    - "VALIDATE: All testing was completed with edge cases"
    - "ENSURE: All todo items are marked complete [x]"
    - "COMPLIANCE: Calculate overall protocol compliance score"

validation_failure_actions:
  missing_verification: "STOP - Agent must add verification citations before proceeding"
  incomplete_research: "STOP - Agent must conduct web research before proceeding"
  testing_insufficient: "STOP - Agent must complete rigorous testing before proceeding"
  task_incomplete: "STOP - Agent must complete all todo items before proceeding"

## ANTI-HALLUCINATION SYSTEM ACTIVATION
# Activate Beast Mode anti-hallucination protocols for all agents

mandatory_verification_enforcement:
  - "BEFORE any technical statement, agent MUST complete verification"
  - "NO technical claims without 'VERIFIED: [source] - [date]' citations"
  - "ALL code syntax must be verified against current documentation"
  - "ALL API usage must be validated with official examples"
  - "ALL best practices must be confirmed through multiple sources"

mandatory_research_enforcement:
  - "THE PROBLEM CAN NOT BE SOLVED WITHOUT EXTENSIVE INTERNET RESEARCH"
  - "Agent MUST use web search for current information"
  - "Agent MUST verify understanding against multiple current sources"
  - "Agent MUST never rely on training data alone for technical decisions"
  - "Agent MUST recursively gather information by following links"

autonomous_completion_enforcement:
  - "Agent MUST iterate and keep going until the problem is solved"
  - "Agent MUST never end turn without completely solving the problem"
  - "Agent MUST create todo list and check off items with [x] syntax"
  - "Agent MUST display updated todo list after each step completion"
  - "Agent MUST continue to next step automatically, not end turn"

rigorous_testing_enforcement:
  - "Agent MUST test code rigorously using provided tools"
  - "Agent MUST test multiple times to catch all edge cases"
  - "Agent MUST use get_errors tool to check for problems"
  - "Agent MUST handle all boundary cases and error conditions"
  - "Agent MUST verify solution works in all scenarios before completion"

# ORCHESTRATOR BEAST MODE INTEGRATION
protocol_reference: ".nexus-core/protocols/beast-mode-protocol.yaml"
protocol_loader: ".nexus-core/utils/protocol-loader.js"
validation_system: ".nexus-core/validation/protocol-validator.yaml"

# ORCHESTRATION WITH VERIFICATION
orchestration_protocol:
  before_task_delegation:
    1: "Load Beast Mode protocols for all agents"
    2: "Verify task requirements through research"
    3: "Ensure receiving agent has current information"
    4: "Mandate verification protocols for all agents"
    5: "Set completion criteria with checkboxes"
    6: "Initialize protocol validation system"
  
  during_task_execution:
    1: "Monitor agent protocol compliance"
    2: "Validate verification citations in real-time"
    3: "Track todo list progression"
    4: "Ensure research protocol adherence"
    5: "Validate testing requirements"
  
  task_completion_validation:
    1: "Verify all protocols followed"
    2: "Validate all citations present"
    3: "Confirm all todo items completed [x]"
    4: "Ensure testing completed"
    5: "Validate autonomous completion"

# AGENT COORDINATION WITH BEAST MODE
agent_coordination:
  protocol_enforcement:
    - "All agents MUST load beast-mode-protocol.yaml"
    - "All agents MUST follow verification protocol"
    - "All agents MUST complete autonomous tasks"
    - "All agents MUST conduct mandatory research"
    - "All agents MUST implement rigorous testing"
  
  validation_checkpoints:
    - "Validate protocol compliance before task handoff"
    - "Check verification citations in agent outputs"
    - "Confirm research conducted with current sources"
    - "Verify testing completed with edge cases"
    - "Ensure todo completion before accepting results"
```

### Step 3: Save the File
Save the updated file with the new protocol integration section.

### Step 4: Validation
1. **Check file location**: Verify file is at `.nexus-core/agents/orchestrator.md`
2. **Check content**: Verify all existing content is preserved
3. **Check additions**: Verify all protocol integration sections are present
4. **Check completeness**: Verify all sections from "BEAST MODE PROTOCOL INTEGRATION" to "validation_failure_actions" are present
5. **Mark complete**: Return to `00-TASK-PROGRESS-TRACKER.md` and mark this task as `[x]`

## ✅ COMPLETION CRITERIA
- [ ] Protocol integration section added to orchestrator.md
- [ ] File saved and validated
- [ ] No existing content removed or modified
- [ ] All protocol references are correct

## 🚨 IMPORTANT NOTES
- **DO NOT** modify existing content - only ADD to the end
- **DO NOT** skip any sections
- **DO NOT** change file paths or names
- **DO NOT** proceed to next task until this is marked complete
- **IMPORTANT**: This content goes at the very end of the existing file
