# TASK 05: CREATE UNCERTAINTY PROTOCOL

## 🎯 OBJECTIVE
Create the uncertainty handling protocol that defines how agents should handle uncertain situations.

## 📁 FILE TO CREATE
**Path**: `.nexus-core/validation/uncertainty-protocol.md`

## 🔧 ACTIONS REQUIRED

### Step 1: Verify Directory Exists
```bash
# The validation directory should exist from previous tasks
# If not, create it:
mkdir -p .nexus-core/validation
```

### Step 2: Create the File
Create the file `.nexus-core/validation/uncertainty-protocol.md` with the following content:

```markdown
# UNCERTAINTY HANDLING PROTOCOL
# References central Beast Mode protocol for uncertainty handling

## Protocol Reference
- Central Protocol: `.nexus-core/protocols/beast-mode-protocol.yaml`
- Section: `uncertainty_handling`

## When Uncertain About ANY Detail:
1. Explicitly state: "I need to verify this information"
2. Conduct research using web + context7
3. Present findings with source attribution
4. If still uncertain, state limitations clearly
5. Provide research-backed alternatives

## FORBIDDEN ACTIONS (from central protocol):
- NEVER guess or provide unverified information
- NEVER proceed without verification
- <PERSON>VER end task without complete validation

## Required Phrases (from central protocol):
- "According to [source]"
- "Based on current documentation"
- "VERIFIED: [source] - [date]"
- "I need to verify this information"

## Escalation Process:
1. If uncertainty persists after research, document limitations
2. Provide multiple research-backed alternatives
3. Include confidence levels for each alternative
4. Cite all sources used in research process

## Uncertainty Categories

### Technical Uncertainty
- **API changes**: Always verify current API documentation
- **Library versions**: Check for latest stable versions
- **Best practices**: Research current community standards
- **Syntax changes**: Verify with official documentation

### Implementation Uncertainty
- **Code patterns**: Research current best practices
- **Architecture decisions**: Verify with multiple sources
- **Performance implications**: Check current benchmarks
- **Security considerations**: Verify with security guidelines

### Process Uncertainty
- **Workflow steps**: Verify with current documentation
- **Tool usage**: Check official tool documentation
- **Integration patterns**: Research current integration methods
- **Testing approaches**: Verify with testing frameworks

## Research Protocol for Uncertainty
1. **Initial Search**: Use web search for current information
2. **Context7 Query**: Check for framework-specific guidance
3. **Official Documentation**: Verify with authoritative sources
4. **Community Sources**: Check recent discussions and issues
5. **Multiple Verification**: Cross-reference across sources

## Documentation Requirements
When uncertain, document:
- **What was uncertain**: Specific details that needed verification
- **Research conducted**: Sources checked and methods used
- **Findings**: What was discovered through research
- **Confidence level**: How certain you are about the solution
- **Sources**: All sources used with proper citations

## Confidence Levels
- **High (90-100%)**: Verified with multiple authoritative sources
- **Medium (70-89%)**: Verified with official documentation
- **Low (50-69%)**: Found in community sources but not official
- **Uncertain (<50%)**: Requires escalation or alternative approaches

## Alternative Approaches
When uncertainty remains:
1. **Provide multiple options**: Present different approaches
2. **Explain trade-offs**: Discuss pros and cons of each
3. **Recommend based on research**: Suggest best option with reasoning
4. **Document assumptions**: Clearly state any assumptions made

## Quality Assurance
Before proceeding after uncertainty:
- [ ] All research sources documented
- [ ] Confidence levels assigned
- [ ] Alternative approaches considered
- [ ] Assumptions clearly stated
- [ ] Next steps defined
- [ ] Escalation path identified if needed

## Success Criteria
Uncertainty handling is successful when:
- [ ] Original uncertainty is clearly documented
- [ ] Comprehensive research is conducted
- [ ] Sources are properly cited
- [ ] Confidence levels are assigned
- [ ] Clear recommendations are provided
- [ ] All assumptions are documented
```

## ✅ COMPLETION CHECKLIST
- [ ] Directory `.nexus-core/validation/` exists
- [ ] File `uncertainty-protocol.md` created in correct location
- [ ] All markdown content copied exactly as specified
- [ ] File saved successfully
- [ ] Markdown formatting is correct

## 🔍 VERIFICATION STEPS
1. **Check file location**: Verify the file exists at `.nexus-core/validation/uncertainty-protocol.md`
2. **Check content**: Open the file and verify it contains all sections from protocol reference to success criteria
3. **Check formatting**: Ensure proper markdown formatting with checkboxes and sections
4. **Mark complete**: Return to `00-TASK-PROGRESS-TRACKER.md` and mark this task as `[x]`

## 🚨 IMPORTANT NOTES
- **DO NOT** modify the content - copy exactly as provided
- **DO NOT** skip any sections
- **DO NOT** change file paths or names
- **DO NOT** proceed to next task until this is marked complete
