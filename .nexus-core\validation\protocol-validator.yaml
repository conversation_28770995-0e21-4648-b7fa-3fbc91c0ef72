# Protocol Validation System
# Ensures all agents follow Beast Mode protocols correctly

validation_system:
  version: "1.0"
  mandatory_checks:
    
    pre_task_validation:
      protocol_loaded: 
        description: "Verify beast-mode-protocol.yaml is loaded"
        required: true
        validation_method: "Check for protocol reference in agent output"
      
      verification_ready:
        description: "Verification tools are available and functional"
        required: true
        validation_method: "Confirm web search and context7 access"
    
    during_task_validation:
      research_conducted:
        description: "Mandatory research protocol followed"
        required: true
        validation_method: "Check for web search evidence and citations"
        
      verification_citations:
        description: "All technical claims have verification citations"
        required: true
        validation_method: "Scan for 'VERIFIED:' format citations"
        
      todo_progression:
        description: "Todo list created and progressed through"
        required: true
        validation_method: "Check for markdown todo format and [x] completion"
        
      continuous_testing:
        description: "Testing performed throughout implementation"
        required: true
        validation_method: "Check for testing evidence and get_errors usage"
    
    post_task_validation:
      complete_verification:
        description: "All outputs verified against current sources"
        required: true
        validation_method: "Verify all technical claims have citations"
        
      autonomous_completion:
        description: "Task completed fully without user intervention"
        required: true
        validation_method: "Check that all todo items are marked [x]"
        
      quality_assurance:
        description: "Output meets quality standards"
        required: true
        validation_method: "Verify testing completed and errors resolved"

  validation_failure_actions:
    missing_verification:
      action: "STOP - Conduct mandatory verification before proceeding"
      remediation: "Research current information and add citations"
      escalation: "Alert supervisor if verification cannot be completed"
      
    incomplete_research:
      action: "STOP - Complete mandatory research protocol"
      remediation: "Use web search and context7 for current information"
      escalation: "Request additional research resources if needed"
      
    testing_insufficient:
      action: "STOP - Implement rigorous testing protocol"
      remediation: "Test all edge cases and error conditions"
      escalation: "Require peer review if testing framework inadequate"
      
    task_incomplete:
      action: "STOP - Follow autonomous completion protocol"
      remediation: "Complete all todo items before ending task"
      escalation: "Supervisor review if task cannot be completed"

  compliance_scoring:
    excellent: 95-100
    good: 85-94
    acceptable: 75-84
    poor: 60-74
    failing: 0-59
    
  reporting_requirements:
    validation_report:
      - "Protocol compliance percentage"
      - "Verification citation count"
      - "Testing coverage assessment"
      - "Todo completion rate"
      - "Quality assurance results"
    
    improvement_recommendations:
      - "Identify weak areas in protocol compliance"
      - "Suggest specific improvements"
      - "Provide training recommendations"
      - "Update protocol based on findings"
