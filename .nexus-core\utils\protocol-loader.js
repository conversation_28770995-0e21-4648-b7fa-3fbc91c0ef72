// Protocol Loader - Ensures all agents load and follow Beast Mode protocols
const yaml = require('js-yaml');
const fs = require('fs');
const path = require('path');

class ProtocolLoader {
  constructor() {
    this.protocolPath = path.join(__dirname, '../protocols/beast-mode-protocol.yaml');
    this.protocols = null;
  }

  async loadProtocols() {
    try {
      const fileContents = fs.readFileSync(this.protocolPath, 'utf8');
      this.protocols = yaml.load(fileContents);
      return this.protocols;
    } catch (error) {
      throw new Error(`Failed to load Beast Mode protocols: ${error.message}`);
    }
  }

  getVerificationProtocol() {
    return this.protocols?.beast_mode_protocol?.verification_protocol;
  }

  getAutonomousCompletionProtocol() {
    return this.protocols?.beast_mode_protocol?.autonomous_completion_protocol;
  }

  getMandatoryResearchProtocol() {
    return this.protocols?.beast_mode_protocol?.mandatory_research_protocol;
  }

  getRigorousTestingProtocol() {
    return this.protocols?.beast_mode_protocol?.rigorous_testing_protocol;
  }

  getUncertaintyHandling() {
    return this.protocols?.beast_mode_protocol?.uncertainty_handling;
  }

  validateProtocolCompliance(taskOutput) {
    const validation = {
      verification_citations: false,
      research_conducted: false,
      testing_completed: false,
      todo_completed: false,
      uncertainty_handled: false
    };

    // Check for verification citations
    if (taskOutput.includes('VERIFIED:')) {
      validation.verification_citations = true;
    }

    // Check for research evidence
    if (taskOutput.includes('According to') || taskOutput.includes('Based on current')) {
      validation.research_conducted = true;
    }

    // Check for testing evidence
    if (taskOutput.includes('tested') || taskOutput.includes('validation')) {
      validation.testing_completed = true;
    }

    // Check for completed todo items
    if (taskOutput.includes('[x]')) {
      validation.todo_completed = true;
    }

    return validation;
  }
}

module.exports = ProtocolLoader;
