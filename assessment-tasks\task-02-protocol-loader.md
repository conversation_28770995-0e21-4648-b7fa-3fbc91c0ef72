# TASK 02: CREATE PROTOCOL LOADER UTILITY

## 🎯 OBJECTIVE
Create the protocol loader utility that helps agents load and validate Beast Mode protocols.

## 📁 FILE TO CREATE
**Path**: `.nexus-core/utils/protocol-loader.js`

## 🔧 ACTIONS REQUIRED

### Step 1: Create Directory Structure
```bash
# Create the utils directory if it doesn't exist
mkdir -p .nexus-core/utils
```

### Step 2: Create the File
Create the file `.nexus-core/utils/protocol-loader.js` with the following content:

```javascript
// Protocol Loader - Ensures all agents load and follow Beast Mode protocols
const yaml = require('js-yaml');
const fs = require('fs');
const path = require('path');

class ProtocolLoader {
  constructor() {
    this.protocolPath = path.join(__dirname, '../protocols/beast-mode-protocol.yaml');
    this.protocols = null;
  }

  async loadProtocols() {
    try {
      const fileContents = fs.readFileSync(this.protocolPath, 'utf8');
      this.protocols = yaml.load(fileContents);
      return this.protocols;
    } catch (error) {
      throw new Error(`Failed to load Beast Mode protocols: ${error.message}`);
    }
  }

  getVerificationProtocol() {
    return this.protocols?.beast_mode_protocol?.verification_protocol;
  }

  getAutonomousCompletionProtocol() {
    return this.protocols?.beast_mode_protocol?.autonomous_completion_protocol;
  }

  getMandatoryResearchProtocol() {
    return this.protocols?.beast_mode_protocol?.mandatory_research_protocol;
  }

  getRigorousTestingProtocol() {
    return this.protocols?.beast_mode_protocol?.rigorous_testing_protocol;
  }

  getUncertaintyHandling() {
    return this.protocols?.beast_mode_protocol?.uncertainty_handling;
  }

  validateProtocolCompliance(taskOutput) {
    const validation = {
      verification_citations: false,
      research_conducted: false,
      testing_completed: false,
      todo_completed: false,
      uncertainty_handled: false
    };

    // Check for verification citations
    if (taskOutput.includes('VERIFIED:')) {
      validation.verification_citations = true;
    }

    // Check for research evidence
    if (taskOutput.includes('According to') || taskOutput.includes('Based on current')) {
      validation.research_conducted = true;
    }

    // Check for testing evidence
    if (taskOutput.includes('tested') || taskOutput.includes('validation')) {
      validation.testing_completed = true;
    }

    // Check for completed todo items
    if (taskOutput.includes('[x]')) {
      validation.todo_completed = true;
    }

    return validation;
  }
}

module.exports = ProtocolLoader;
```

## ✅ COMPLETION CHECKLIST
- [ ] Directory `.nexus-core/utils/` created
- [ ] File `protocol-loader.js` created in correct location
- [ ] All JavaScript content copied exactly as specified
- [ ] File saved successfully
- [ ] No syntax errors in the code

## 🔍 VERIFICATION STEPS
1. **Check file location**: Verify the file exists at `.nexus-core/utils/protocol-loader.js`
2. **Check content**: Open the file and verify it contains the complete `ProtocolLoader` class
3. **Check syntax**: Ensure proper JavaScript syntax (no missing brackets, semicolons, etc.)
4. **Mark complete**: Return to `00-TASK-PROGRESS-TRACKER.md` and mark this task as `[x]`

## 🚨 IMPORTANT NOTES
- **DO NOT** modify the content - copy exactly as provided
- **DO NOT** skip any sections
- **DO NOT** change file paths or names
- **DO NOT** proceed to next task until this is marked complete
